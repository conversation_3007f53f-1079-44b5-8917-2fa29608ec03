package com.yxt.invoice.infrastructure.provider.dto.pos;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class PosResponse<T> implements Serializable {

  private String rnId; // 响应ID
  private String status; // 0-失败，1-成功
  private String msg; // 提示消息


  @ApiModelProperty("请求返回实体对象")
  private T data;

  public Boolean success() {
    return "0".equals(status);
  }

}
